import { notification, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import Constants, { DEFAULT_CUR_ROUND_OFF, toISTDate } from '@Apis/constants';
import PRZSelect from '../../../Common/UI/PRZSelect';
import HideValue from '@Components/Common/RestrictedAccess/HideValue';
import { ACTIONS } from './reducer';
import Helpers from '@Apis/helpers';
const { Option } = Select;
const SelectOrdersForGRN = ({ disabled, grnTypeValue, mode, onChange, selectedPoValue, purchaseOrders, isDataMaskingPolicyEnable, isHideCostPrice, MONEY, loading, inputClassName, getPurchaseOrdersV2, selectedTenant, selectedTenantSeller }) => {

  const [searchByPONumber, setSearchByPONumber] = useState('');

  useEffect(() => {
    const handler = setTimeout(() => {
      const excludePo = grnTypeValue === 'MULTIPO';
      getPurchaseOrdersV2({
        query: {
          tenant_id: selectedTenant,
          tenant_seller_id: selectedTenantSeller,
          status: 'ISSUED',
          page: 1,
          limit: 30,
          exclude_job_works_po: excludePo,
          exclude_subcontractor_po: excludePo,
          search_keyword: searchByPONumber,
        },
      });
    }, 500); // 500ms debounce time
    return () => {
      clearTimeout(handler);
    };
  }, [searchByPONumber]);
  return (
    <PRZSelect
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      placeholder="Select purchase order.."
      labelInValue
      disabled={disabled}
      showSearch
      // allowClear
      value={selectedPoValue}
      maxTagCount="responsive"
      mode={mode}
      onChange={onChange}
      loading={loading}
      className={inputClassName}
      onSearch={(value) => setSearchByPONumber(value)}
      filterOption={false}
    >
      {purchaseOrders?.purchase_order
        ?.filter((item) => item?.closing_status !== 'CLOSED')
        ?.map((item) => (
          <Option key={item.po_id} value={item.po_id}>
            {`#${item.po_number} (${toISTDate(item.created_date || item.created_at).format(
              'DD/MM/YYYY'
            )}) - `}
            {isDataMaskingPolicyEnable && isHideCostPrice ? (
              <HideValue
                showPopOver
                popOverMessage={'You don\'t have access to view po amount'}
              />
            ) : (
              MONEY(
                item.po_grand_total,
                item?.org_currency_info?.currency_code
              )
            )}
          </Option>
        ))}
    </PRZSelect>
  );
};

export default SelectOrdersForGRN;

export const calculateCostPrice = (item, selectedPo, isApInvoiceEnabled) => {
  // If AP Invoice is enabled, cost price should be 0
  if (isApInvoiceEnabled) {
    return 0;
  }

  // If no offer price, return 0
  if (!item?.offer_price) {
    return 0;
  }

  // If conversion rate exists, apply it and round to default currency precision
  if (selectedPo?.conversion_rate) {
    const convertedPrice = item.offer_price * selectedPo.conversion_rate;
    return Number.parseFloat(convertedPrice.toFixed(DEFAULT_CUR_ROUND_OFF));
  }

  // Return the original offer price
  return item.offer_price;
};

export const getClassNameHelper = (isFlexible, isOverflow, item, formSubmitted) => {
  if (formSubmitted && (Number(item?.received_qty) <= 0)) {
    return 'orgFormInputError';
  }
  if (isFlexible) {
    return '';
  }
  if (formSubmitted && (Number(item?.quantity) - Number(item?.total_received_qty) - Number(item?.received_qty) < 0) || isOverflow) {
    return 'orgFormInputError';
  }

  return '';
};

export const getLineTotals = (
  data,
  chargeData,
  charge1Value,
  grnTypeValue,
  taxTypeInfo,
  taxTypeName,
  freightTax,
  freightTaxData
) => {
  data = data ?? [];
  chargeData = chargeData ?? [];
  charge1Value = charge1Value ?? 0;

  let totalAmount = 0;
  let totalDiscount = 0;
  let totalBase = 0;
  let totalTcs = 0;
  let totalTds = 0;
  let totalTax = 0;
  let totalOtherCharge = 0;
  let totalOtherChargeWithOutTax = 0;
  let grnTotal = 0;
  let totalTaxValue = 0;
  let freightCharge = Number(charge1Value);
  let totalOtherChargesForTaxableAmount = 0;
  let freightTaxAmount = 0;

  // Taxes bifurcation
  if (data?.length > 0) {
    totalTaxValue = Helpers.groupAndSumByTaxName(
      data?.map((item) => item?.child_taxes)?.flat()
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
  }

  for (let i = 0; i < chargeData?.length; i++) {
    let currentOtherCharge = 0;
    let currentOtherChargeWithoutTax = 0;
    let chargesTaxAmountLinesWise = 0;

    if (chargeData[i]?.charge_amount) {
      chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName(
        [...chargeData[i]?.chargesTaxData?.child_taxes?.flat()]
      )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

      currentOtherCharge = chargeData?.[i]?.chargesTax
        ? chargeData?.[i]?.charge_amount + chargesTaxAmountLinesWise
        : chargeData?.[i]?.charge_amount;

      currentOtherChargeWithoutTax = chargeData?.[i]?.charge_amount;

      if (chargeData?.[i]?.chargesTax) {
        totalOtherChargesForTaxableAmount += chargeData?.[i]?.charge_amount;
      }
    }

    totalOtherCharge += currentOtherCharge;
    totalOtherChargeWithOutTax += currentOtherChargeWithoutTax;
  }

  let lines = [];

  for (let i = 0; i < data?.length; i++) {
    let currentAmount = 0;
    let currentDiscount = 0;
    let currentBase = 0;
    let currentTax = 0;
    let currentGRN = 0;

    const quantityToUse =
      grnTypeValue === 'ADHOC' ? data[i]?.quantity : data[i]?.received_qty;

    const discountValue = Number(data[i].discount);

    if (quantityToUse) {
      currentAmount += quantityToUse * data[i].offer_price;

      if (discountValue) {
        if (data[i]?.lineDiscountType === 'Percent') {
          currentDiscount +=
            quantityToUse * data[i].offer_price * (discountValue / 100);

          currentBase =
            (quantityToUse * data[i].offer_price * (100 - discountValue)) / 100;
        } else if (data[i]?.lineDiscountType === 'Amount') {
          currentDiscount += discountValue;
          currentBase += quantityToUse * data[i].offer_price - discountValue;
        }
      } else {
        currentDiscount += 0;
        currentBase += quantityToUse * data[i].offer_price;
      }
    }

    // Subtract TDS from currentBase or Add TCS
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      const tcsAmount = currentBase * tcsRate;
      totalTcs += tcsAmount;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      const tdsAmount = currentBase * tdsRate;
      totalTds -= tdsAmount;
    }

    if (data[i]?.taxInfo?.tax_value || data[i]?.taxInfo?.[0]?.tax_value) {
      currentTax =
        currentBase *
        ((data[i]?.taxInfo?.tax_value || data[i]?.taxInfo?.[0]?.tax_value) /
          100);
    }

    if (currentBase) currentGRN = currentBase;

    totalAmount += currentAmount;
    totalDiscount += currentDiscount;
    totalBase += currentBase;
    totalTax += currentTax;
    grnTotal += currentGRN;

    lines.push({
      grn_line_total: currentGRN + currentTax + (taxTypeName === 'TCS' ? totalTcs : totalTds),
      grn_line_quantity: quantityToUse,
      grn_line_unit_price: data[i].offer_price,
    });
  }

  grnTotal += totalTaxValue;

  lines = lines.map((line) => {
    let unitOtherCharge = 0;
    let unitFreightCharge = 0;
    const unitCostPrice = Number(line.grn_line_unit_price);

    unitOtherCharge = Number(
      ((line.grn_line_total / grnTotal) * totalOtherChargeWithOutTax) /
        line.grn_line_quantity
    );

    unitFreightCharge = Number(
      ((line.grn_line_total / grnTotal) * Number(charge1Value)) /
        line.grn_line_quantity
    );

    return {
      ...line,
      unit_landed_cost:
        Number(unitCostPrice + unitOtherCharge + unitFreightCharge).toFixed(2) ||
        0,
      unit_other_cost: Number(unitOtherCharge).toFixed(2) || 0,
      unit_freight_cost: Number(unitFreightCharge).toFixed(2) || 0,
    };
  });

  if (freightTax) {
    freightTaxAmount = Helpers.groupAndSumByTaxName(
      [...freightTaxData?.child_taxes?.flat()]
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

    totalBase += freightCharge;
    freightCharge += freightTaxAmount;

    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      totalTcs = totalBase * tcsRate;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      totalTds = -((totalBase + totalOtherChargesForTaxableAmount) * tdsRate);
    }
  }

  if (taxTypeInfo && taxTypeName === 'TCS') {
    const tcsRate = taxTypeInfo?.tax_value / 100;
    const tcsAmount =
      (totalBase +
        totalTaxValue +
        Number(totalOtherCharge) +
        (freightTax ? freightTaxAmount : freightCharge)) *
      tcsRate;
    totalTcs = tcsAmount;
  }

  grnTotal += taxTypeName === 'TCS' ? totalTcs : totalTds;
  grnTotal += totalOtherCharge;
  grnTotal += freightCharge;
  totalBase += totalOtherChargesForTaxableAmount;

  return {
    totalAmount,
    totalDiscount,
    totalBase,
    totalTax,
    totalTcs,
    totalTds,
    grnTotal,
    lines,
  };
};
